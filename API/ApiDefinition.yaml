openapi: 3.0.3
info:
  title: CMap Email Filing API
  description: |
    Azure Functions API for CMap Email Filing system that handles email filing,
    subscription management, and SharePoint integration.
  version: 1.0.0
  contact:
    name: CMap Email Filing Support
    email: <EMAIL>
  license:
    name: Proprietary

servers:
  - url: https://atvero-fa-emailfiling-prod.azurewebsites.net/api
    description: Production server
  - url: https://atvero-fa-emailfiling-staging.azurewebsites.net/api
    description: Staging server
  - url: https://atvero-fa-emailfiling-dev.azurewebsites.net/api
    description: Development server

security:
  - BearerAuth: []

paths:
  /GetInfo:
    get:
      summary: Get filing information for an email
      description: Retrieves filing information for a specific email by its Internet Message ID
      tags:
        - Email Filing
      parameters:
        - name: internetMessageId
          in: query
          required: true
          description: The Internet Message ID of the email
          schema:
            type: string
            example: "<<EMAIL>>"
      responses:
        '200':
          description: Successfully retrieved filing information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Filing information retrieved successfully"
                data:
                  - projectCode: "PROJ001"
                    internetMessageId: "<<EMAIL>>"
                    tag: "Legal"
                    confidential: true
                    important: false
                    filedBy: "<EMAIL>"
                    timestamp: "2024-01-15T10:30:00Z"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /EnqueueEmailRequest:
    post:
      summary: Enqueue email filing request
      description: Adds an email filing request to the processing queue
      tags:
        - Email Filing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileEmailRequest'
      responses:
        '200':
          description: Email filing request successfully enqueued
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /RequestFileConversation:
    post:
      summary: Request filing of an entire conversation
      description: Files all emails in a conversation thread
      tags:
        - Email Filing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileConversationRequest'
      responses:
        '200':
          description: Conversation filing request processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /CheckConversationStatus:
    get:
      summary: Check if a conversation is already filed
      description: Checks the filing status of a conversation
      tags:
        - Email Filing
      parameters:
        - name: conversationid
          in: query
          required: true
          description: The conversation ID to check
          schema:
            type: string
        - name: sharedmailbox
          in: query
          required: false
          description: Shared mailbox to check (if applicable)
          schema:
            type: string
      responses:
        '200':
          description: Conversation status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: ""
                data:
                  status: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /Hubsites:
    get:
      summary: Get available hub sites
      description: Retrieves list of SharePoint hub sites available for filing
      tags:
        - SharePoint
      responses:
        '200':
          description: Hub sites retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Hub sites retrieved successfully"
                data:
                  - name: "Legal"
                    siteUrl: "https://tenant.sharepoint.com/sites/legal"
                    displayName: "Legal Department"
                    customTags: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /Projects:
    get:
      summary: Get projects for a hub site
      description: Retrieves list of projects available in a specific hub site
      tags:
        - SharePoint
      parameters:
        - name: hubsite
          in: query
          required: true
          description: The hub site URL or identifier
          schema:
            type: string
      responses:
        '200':
          description: Projects retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Projects retrieved successfully"
                data:
                  - projectCode: "PROJ001"
                    projectTitle: "Project Alpha"
                    id: "1"
                    rank: 1
                    sitePath: "/sites/proj001"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /CheckUserSubscription:
    get:
      summary: Check user's email subscriptions
      description: Retrieves list of active email subscriptions for the authenticated user
      tags:
        - Subscriptions
      parameters:
        - name: sharedmailbox
          in: query
          required: false
          description: Check subscriptions for a specific shared mailbox
          schema:
            type: string
      responses:
        '200':
          description: User subscriptions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: ""
                data:
                  - subscription: "subscription-id-1"
                  - subscription: "subscription-id-2"
        '401':
          $ref: '#/components/responses/Unauthorized'

  /StoreUserCredentials:
    get:
      summary: Store user credentials and create subscriptions
      description: OAuth callback endpoint that stores user credentials and creates email subscriptions
      tags:
        - Subscriptions
      parameters:
        - name: code
          in: query
          required: true
          description: OAuth authorization code
          schema:
            type: string
        - name: state
          in: query
          required: true
          description: OAuth state parameter containing client state
          schema:
            type: string
      responses:
        '200':
          description: Credentials stored and subscriptions created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /ClearSubscriptions:
    get:
      summary: Clear user's email subscriptions
      description: Removes all email subscriptions for the authenticated user
      tags:
        - Subscriptions
      parameters:
        - name: sharedmailbox
          in: query
          required: false
          description: Clear subscriptions for a specific shared mailbox
          schema:
            type: string
      responses:
        '200':
          description: Subscriptions cleared successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /IncomingSubscription:
    post:
      summary: Handle incoming subscription notifications
      description: Webhook endpoint for Microsoft Graph subscription notifications
      tags:
        - Webhooks
      parameters:
        - name: validationToken
          in: query
          required: false
          description: Validation token for subscription setup
          schema:
            type: string
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionRequest'
      responses:
        '200':
          description: Subscription notification processed successfully
        '202':
          description: Notification accepted for processing
        '400':
          $ref: '#/components/responses/BadRequest'

  /LifeCycleNotifications:
    post:
      summary: Handle subscription lifecycle notifications
      description: Webhook endpoint for Microsoft Graph subscription lifecycle events
      tags:
        - Webhooks
      parameters:
        - name: validationToken
          in: query
          required: false
          description: Validation token for subscription setup
          schema:
            type: string
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LifecycleRequest'
      responses:
        '200':
          description: Lifecycle notification processed successfully
        '202':
          description: Notification accepted for processing
        '400':
          $ref: '#/components/responses/BadRequest'

  /ForwardMail:
    post:
      summary: Forward email with SharePoint attachment
      description: Creates a forward of an email with a SharePoint file attachment
      tags:
        - Email Operations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForwardEmailRequest'
      responses:
        '200':
          description: Email forwarded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /CustomTags:
    get:
      summary: Get custom tags for a hub site
      description: Retrieves custom filing tags available for a specific hub site
      tags:
        - SharePoint
      parameters:
        - name: hubsite
          in: query
          required: true
          description: The hub site URL or identifier
          schema:
            type: string
      responses:
        '200':
          description: Custom tags retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Custom tags retrieved successfully"
                data:
                  - name: "Legal"
                    colour: "#FF0000"
                    backgroundColour: "#FFEEEE"
                    order: 1
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /GetPimRecords:
    get:
      summary: Get PIM records context
      description: |
        Creates a Context for use with the CMapPim project. Accepts either a record name
        (which will be used to construct a SharePoint site URL) or a full site URL.
      tags:
        - PIM
      parameters:
        - name: recordName
          in: query
          required: false
          description: |
            The record name to use for constructing the SharePoint site URL.
            Either recordName or siteUrl must be provided.
          schema:
            type: string
            example: "project-alpha"
        - name: siteUrl
          in: query
          required: false
          description: |
            The full SharePoint site URL to use directly.
            Either recordName or siteUrl must be provided.
          schema:
            type: string
            format: uri
            example: "https://tenant.sharepoint.com/sites/project-alpha"
      responses:
        '200':
          description: PIM context created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Context created successfully for CMapPim project"
                data:
                  siteUrl: "https://tenant.sharepoint.com/sites/project-alpha"
                  recordName: "project-alpha"
                  message: "Context created successfully for CMapPim project"
                  usedProvidedSiteUrl: false
        '400':
          description: Bad Request - Missing required parameters or invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: false
                message: "Either recordName or siteUrl parameter is required."
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /AddHubSite:
    post:
      summary: Add a new SharePoint hub site
      description: Adds SharePoint hub site details to the Hubsite Azure table for a customer domain
      tags:
        - SharePoint
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddHubSiteRequest'
      responses:
        '200':
          description: Hub site added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Hub site added successfully"
                data:
                  customerDomain: "example.com"
                  hubSiteName: "Legal"
                  siteUrl: "https://tenant.sharepoint.com/sites/legal"
                  customTags: true
                  timestamp: "2024-01-15T10:30:00Z"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: Forbidden - Cannot add hub site for different domain
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: false
                message: "You can only add hub sites for your own domain"
        '409':
          description: Conflict - Hub site already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: false
                message: "Hub site with this name already exists for the domain"
        '500':
          $ref: '#/components/responses/InternalServerError'

  /GetHubsiteRights:
    get:
      summary: Get users with hub site association rights
      description: Retrieves a list of users who can associate sites with the specified SharePoint hub site
      tags:
        - SharePoint
      parameters:
        - name: sitePath
          in: query
          required: true
          description: The SharePoint hub site URL
          schema:
            type: string
            format: uri
            example: "https://tenant.sharepoint.com/sites/hubsite"
      responses:
        '200':
          description: Hub site rights retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Successfully retrieved hub site rights for 3 users"
                data:
                  sitePath: "https://tenant.sharepoint.com/sites/hubsite"
                  usersWithRights:
                    - "<EMAIL>"
                    - "<EMAIL>"
                    - "<EMAIL>"
                  totalUsers: 3
                  retrievedAt: "2024-01-15T10:30:00Z"
        '400':
          description: Bad Request - Invalid or missing sitePath parameter
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: false
                message: "sitePath query parameter is required"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: Forbidden - Insufficient permissions to access hub site rights
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: false
                message: "Insufficient permissions to access hub site rights"
        '500':
          $ref: '#/components/responses/InternalServerError'

  /ExportStats:
    get:
      summary: Export statistics data as CSV
      description: |
        Exports statistics data from various Azure Tables as CSV files. Supports multiple statistics types
        and time periods with flexible date range filtering. Requires API key authentication.
      tags:
        - Statistics
      security:
        - ApiKeyAuth: []
      parameters:
        - name: statsType
          in: query
          required: true
          description: The type of statistics to export
          schema:
            type: string
            enum: [UserStats, DomainStats, AutoFilingStats, UserClickStats, ProjectStats]
            example: "UserStats"
        - name: period
          in: query
          required: true
          description: The time period type for the export
          schema:
            type: string
            enum: [weekly, monthly]
            example: "weekly"
        - name: startYear
          in: query
          required: true
          description: The starting year (4-digit year)
          schema:
            type: integer
            minimum: 2020
            maximum: 2030
            example: 2024
        - name: endYear
          in: query
          required: false
          description: The ending year (defaults to startYear if not provided)
          schema:
            type: integer
            minimum: 2020
            maximum: 2030
            example: 2024
        - name: week
          in: query
          required: false
          description: Export a single week (1-53). For weekly period only.
          schema:
            type: integer
            minimum: 1
            maximum: 53
            example: 15
        - name: startWeek
          in: query
          required: false
          description: Starting week for range export (1-53). For weekly period only.
          schema:
            type: integer
            minimum: 1
            maximum: 53
            example: 10
        - name: endWeek
          in: query
          required: false
          description: Ending week for range export (1-53). For weekly period only.
          schema:
            type: integer
            minimum: 1
            maximum: 53
            example: 20
        - name: month
          in: query
          required: false
          description: Export a single month (1-12). For monthly period only.
          schema:
            type: integer
            minimum: 1
            maximum: 12
            example: 6
        - name: startMonth
          in: query
          required: false
          description: Starting month for range export (1-12). For monthly period only.
          schema:
            type: integer
            minimum: 1
            maximum: 12
            example: 1
        - name: endMonth
          in: query
          required: false
          description: Ending month for range export (1-12). For monthly period only.
          schema:
            type: integer
            minimum: 1
            maximum: 12
            example: 6
        - name: apiKey
          in: query
          required: false
          description: API key for authentication (alternative to header-based auth)
          schema:
            type: string
            example: "your-secret-api-key"
      responses:
        '200':
          description: Statistics data exported successfully as CSV
          headers:
            Content-Type:
              description: MIME type of the response
              schema:
                type: string
                example: "text/csv"
            Content-Disposition:
              description: Attachment filename for download
              schema:
                type: string
                example: 'attachment; filename="UserStats_weekly_2024-2024_W10-20_20240315_143000.csv"'
          content:
            text/csv:
              schema:
                type: string
                description: CSV data with statistics
                example: |
                  Period,CustomerDomain,RowKey,WeeklyCount
                  2024-10_example.com,example.com,<EMAIL>,25
                  2024-11_example.com,example.com,<EMAIL>,30
        '400':
          description: Bad Request - Invalid query parameters
          content:
            text/plain:
              schema:
                type: string
                example: "Invalid query parameters"
        '401':
          description: Unauthorized - Invalid or missing API key
          content:
            text/plain:
              schema:
                type: string
                example: "Unauthorized - Invalid API key"
        '500':
          description: Internal Server Error
          content:
            text/plain:
              schema:
                type: string
                example: "Internal server error"

  /SaveAttachmentsAsReceived:
    post:
      summary: Save attachments as received
      description: |
        Processes a list of email attachments and saves them as received records in SharePoint.
        Takes a SharePoint site URL and a list of attachment records containing email ID,
        record name, record title, and revision information.
      tags:
        - Email Filing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveAttachmentsAsReceivedRequest'
      responses:
        '200':
          description: Attachments processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: true
                message: "Attachments saved as received successfully"
                data:
                  siteUrl: "https://tenant.sharepoint.com/sites/project"
                  processedAttachments: 3
                  processedAt: "2024-01-15T10:30:00Z"
                  message: "Attachments processed successfully"
        '400':
          description: Bad Request - Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                isSuccess: false
                message: "Invalid request data. SiteUrl and Attachments are required."
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /Debug:
    post:
      summary: Debug endpoint for testing
      description: Debug endpoint for development and testing purposes
      tags:
        - Debug
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                testData:
                  type: string
      responses:
        '200':
          description: Debug operation completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from Microsoft Graph authentication

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API key for ExportStats endpoint authentication. Can be provided via:
        - X-API-Key header
        - Authorization header with "ApiKey " prefix
        - apiKey query parameter

  schemas:
    ApiResponse:
      type: object
      properties:
        isSuccess:
          type: boolean
          description: Indicates if the operation was successful
        message:
          type: string
          description: Response message
        data:
          description: Response data (type varies by endpoint)
      required:
        - isSuccess
        - message

    FileEmailRequest:
      type: object
      properties:
        hubSite:
          type: string
          description: Hub site URL or identifier
        sharedMailbox:
          type: string
          description: Shared mailbox (if applicable)
        projectCode:
          type: string
          description: Project code for filing
        sitePath:
          type: string
          description: SharePoint site path
        siteId:
          type: string
          description: SharePoint site ID
        tag:
          type: string
          description: Filing tag/category
        emails:
          type: array
          items:
            $ref: '#/components/schemas/Email'
          description: List of emails to file
        isConfidential:
          type: boolean
          description: Mark as confidential
        isImportant:
          type: boolean
          description: Mark as important
      required:
        - projectCode
        - emails

    FileConversationRequest:
      type: object
      properties:
        hubSite:
          type: string
          description: Hub site URL or identifier
        sharedMailbox:
          type: string
          description: Shared mailbox (if applicable)
        conversationId:
          type: string
          description: Conversation ID to file
        projectCode:
          type: string
          description: Project code for filing
        tag:
          type: string
          description: Filing tag/category
        emails:
          type: array
          items:
            $ref: '#/components/schemas/Email'
          description: List of emails in conversation
        isConfidential:
          type: boolean
          description: Mark as confidential
        isImportant:
          type: boolean
          description: Mark as important
      required:
        - conversationId
        - projectCode

    Email:
      type: object
      properties:
        itemId:
          type: string
          description: Email item ID
      required:
        - itemId

    ForwardEmailRequest:
      type: object
      properties:
        driveId:
          type: string
          description: SharePoint drive ID
        driveItemId:
          type: string
          description: SharePoint drive item ID
      required:
        - driveId
        - driveItemId

    SubscriptionRequest:
      type: object
      properties:
        value:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionRequestItem'

    SubscriptionRequestItem:
      type: object
      properties:
        subscriptionId:
          type: string
        subscriptionExpirationDateTime:
          type: string
          format: date-time
        changeType:
          type: string
        resource:
          type: string
        clientState:
          type: string
        tenantId:
          type: string

    LifecycleRequest:
      type: object
      properties:
        value:
          type: array
          items:
            $ref: '#/components/schemas/LifecycleRequestItem'

    LifecycleRequestItem:
      type: object
      properties:
        subscriptionId:
          type: string
        subscriptionExpirationDateTime:
          type: string
          format: date-time
        lifecycleEvent:
          type: string
          enum: [reauthorizationRequired, subscriptionRemoved, missed]
        tenantId:
          type: string
        clientState:
          type: string

    Project:
      type: object
      properties:
        projectCode:
          type: string
          description: Unique project code
        projectTitle:
          type: string
          description: Project title/name
        id:
          type: string
          description: Project ID
        rank:
          type: integer
          description: Project ranking/priority
        sitePath:
          type: string
          description: SharePoint site path
      required:
        - projectCode
        - projectTitle
        - id

    GetInfoResponse:
      type: object
      properties:
        projectCode:
          type: string
          description: Project code where email was filed
        internetMessageId:
          type: string
          description: Internet Message ID of the email
        tag:
          type: string
          description: Filing tag/category
        confidential:
          type: boolean
          description: Whether email is marked as confidential
        important:
          type: boolean
          description: Whether email is marked as important
        sharedMailbox:
          type: string
          description: Shared mailbox if applicable
        filedBy:
          type: string
          description: User who filed the email
        timestamp:
          type: string
          format: date-time
          description: When the email was filed

    HubSite:
      type: object
      properties:
        name:
          type: string
          description: Hub site name/identifier
        siteUrl:
          type: string
          description: SharePoint site URL
        displayName:
          type: string
          description: Display name of the hub site
        customTags:
          type: boolean
          description: Whether custom tags are enabled
      required:
        - name
        - siteUrl
        - displayName

    CustomTag:
      type: object
      properties:
        name:
          type: string
          description: Tag name
        colour:
          type: string
          description: Tag text color (hex)
        backgroundColour:
          type: string
          description: Tag background color (hex)
        order:
          type: integer
          description: Display order
      required:
        - name
        - colour
        - backgroundColour

    Subscription:
      type: object
      properties:
        subscription:
          type: string
          description: Subscription ID
      required:
        - subscription

    UnfileEmailRequest:
      type: object
      properties:
        driveId:
          type: string
          description: SharePoint drive ID
        id:
          type: string
          description: Item ID
        messageId:
          type: string
          description: Email message ID
        conversationId:
          type: string
          description: Conversation ID
        driveItemId:
          type: string
          description: SharePoint drive item ID
        projectCode:
          type: string
          description: Project code
      required:
        - messageId
        - projectCode

    AddHubSiteRequest:
      type: object
      properties:
        customerDomain:
          type: string
          description: Customer domain for the hub site
          example: "example.com"
        hubSiteName:
          type: string
          description: Name/identifier for the hub site
          example: "Legal"
        siteUrl:
          type: string
          format: uri
          description: SharePoint hub site URL
          example: "https://tenant.sharepoint.com/sites/legal"
        customTags:
          type: boolean
          description: Whether custom tags are enabled for this hub site
          default: false
      required:
        - customerDomain
        - hubSiteName
        - siteUrl

    HubsiteRightsResponse:
      type: object
      properties:
        sitePath:
          type: string
          format: uri
          description: SharePoint hub site URL
        usersWithRights:
          type: array
          items:
            type: string
          description: List of user login names with hub site association rights
        totalUsers:
          type: integer
          description: Total number of users with rights
        retrievedAt:
          type: string
          format: date-time
          description: Timestamp when the rights were retrieved
      required:
        - sitePath
        - usersWithRights
        - totalUsers
        - retrievedAt

    SaveAttachmentsAsReceivedRequest:
      type: object
      properties:
        siteUrl:
          type: string
          format: uri
          description: Full SharePoint site URL where attachments will be processed
          example: "https://tenant.sharepoint.com/sites/project-alpha"
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/AttachmentRecord'
          description: List of attachment records to process
          minItems: 1
      required:
        - siteUrl
        - attachments

    AttachmentRecord:
      type: object
      properties:
        emailId:
          type: string
          description: Email identifier or message ID
          example: "AAMkAGVmMDEzMTM4LTZmYWUtNDdkNC1hMDZiLTU1OGY5OTZhYmY4OABGAAAAAAAiQ8W967B7TKBjgx9rVEURBwAiIsqMbYjsT5e-T7KzowPTAAAAAAEMAAAiIsqMbYjsT5e-T7KzowPTAAAYNKhwAAA="
        recordName:
          type: string
          description: Name of the record
          example: "DOC-001"
        recordTitle:
          type: string
          description: Title of the record
          example: "Project Requirements Document"
        revision:
          type: string
          description: Revision number or identifier
          example: "Rev-A"
      required:
        - emailId
        - recordName
        - recordTitle

    ErrorDetails:
      type: object
      properties:
        error:
          type: string
          description: Error type
        message:
          type: string
          description: Error message
        details:
          type: string
          description: Additional error details

  responses:
    Unauthorized:
      description: Authentication required or token invalid
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            isSuccess: false
            message: "Failed to validate authorization token"

    BadRequest:
      description: Invalid request parameters or body
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            isSuccess: false
            message: "Invalid request data"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            isSuccess: false
            message: "An unexpected error occurred"

tags:
  - name: Email Filing
    description: Operations related to filing emails to SharePoint
  - name: SharePoint
    description: Operations for retrieving SharePoint sites and projects
  - name: Subscriptions
    description: Management of email subscription notifications
  - name: Webhooks
    description: Webhook endpoints for Microsoft Graph notifications
  - name: Email Operations
    description: Email-related operations like forwarding
  - name: Statistics
    description: Export and analysis of system usage statistics
  - name: PIM
    description: PIM (Project Information Management) related operations
  - name: Debug
    description: Debug and testing endpoints
