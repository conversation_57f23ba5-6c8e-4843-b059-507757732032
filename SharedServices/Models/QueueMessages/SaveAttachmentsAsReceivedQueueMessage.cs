using AtveroEmailFiling.Models.ApiRequests;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.QueueMessages
{
    public class SaveAttachmentsAsReceivedQueueMessage
    {
        [JsonProperty("token")]
        public string? Token { get; set; }

        [JsonProperty("userId")]
        public string? UserId { get; set; }

        [JsonProperty("customerDomain")]
        public string? CustomerDomain { get; set; }

        [JsonProperty("siteUrl")]
        public string? SiteUrl { get; set; }

        [JsonProperty("siteId")]
        public string? SiteId { get; set; }

        [JsonProperty("emailId")]
        public string? EmailId { get; set; }

        [JsonProperty("attachments")]
        public List<AttachmentRecord>? Attachments { get; set; }
    }
}
