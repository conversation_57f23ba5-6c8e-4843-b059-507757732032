using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AtveroEmailFiling.Models.ApiRequests;
using CMapPim.GeneratedModels;
using CMapPim.Model;
using CMapPim.Services;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;

namespace CMapPim.Utils;

public static class ReceivedTransmittalUtils
{
    public static async Task<Result> CreateReceivedTransmittalFromEmail(
        Context context,
        ICMapPimService pimService,
        string emailId,
        string activeUser,
        List<AttachmentRecord> attachmentsToFile
    )
    {
        // Get email
        FluentResults.Result<Microsoft.Graph.Models.Message> email =
            await context.GraphApiClient.GetEmailByIdAsync(
                emailId,
                context.GraphClient,
                activeUser
            );

        if (email.IsFailed)
        {
            return email.ToResult();
        }

        foreach (AttachmentRecord attachmentRecord in attachmentsToFile)
        {
            Attachment? attachment = email.Value.Attachments?.FirstOrDefault(a =>
                a.Id == attachmentRecord.AttachementId
            );
            if (attachment == null)
            {
                context.Logger.LogError(
                    "Failed to find attachment with ID {AttachmentId}",
                    attachmentRecord.AttachementId
                );

                continue;
            }

            PlaceholderBase newRecord = new(context.Logger)
            {
                Title = attachmentRecord.RecordName,
                ATVDocumentNumber = attachmentRecord.RecordName,
                ATVDocumentTitle = attachmentRecord.RecordTitle,
                ATVDateKey = email.Value.ReceivedDateTime?.UtcDateTime,
                ATVControlLifecycle = "Yes",
            };
            Result<PlaceholderBase> record = await pimService.CreateRecord(context, newRecord);

            if (record.IsFailed)
            {
                context.Logger.LogError(
                    "Failed to create record for {RecordName}",
                    attachmentRecord.RecordName
                );
                context.Logger.LogInformation(record.ToString());

                continue;
            }

            RevisionBase newRevision = new(context.Logger)
            {
                ATVRevision = attachmentRecord.Revision,
                Title = attachmentRecord.Revision,

                ATVReceived = true,
            };

            Result<RevisionBase> revision = await pimService.CreateRevision(
                context,
                record.Value,
                newRevision
            );

            // Create received revision

            if (revision.IsFailed)
            {
                context.Logger.LogError(
                    "Failed to create received revision for {RecordName}",
                    attachmentRecord.RecordName
                );
                context.Logger.LogInformation(revision.ToString());

                continue;
            }

            Result<MemoryStream> attachmentResult =
                await context.GraphApiClient.GetEmailAttachmentContentByIdAsync(
                    emailId,
                    attachmentRecord.AttachementId,
                    context.GraphClient,
                    activeUser
                );

            if (attachmentResult.IsFailed)
            {
                context.Logger.LogError(
                    "Failed to get attachment with ID {AttachmentId}",
                    attachmentRecord.AttachementId
                );
                context.Logger.LogInformation(attachmentResult.ToString());

                continue;
            }

            Result<MemoryStream> attachmentStreamResult =
                await context.GraphApiClient.GetEmailAttachmentContentByIdAsync(
                    emailId,
                    attachmentRecord.AttachementId,
                    context.GraphClient,
                    activeUser
                );

            if (attachmentStreamResult.IsFailed)
            {
                context.Logger.LogError(
                    "Failed to get attachment stream with ID {AttachmentId}",
                    attachmentRecord.AttachementId
                );
                context.Logger.LogInformation(attachmentStreamResult.ToString());

                continue;
            }

            Result<RevisionBase> rendition = await pimService.CreateRendition(
                context,
                record.Value,
                revision.Value,
                attachmentStreamResult.Value
            );
            // Save attachments
        }

        // Update recevied revision

        // Create recieved transmittal

        return Result.Ok();
    }
}
