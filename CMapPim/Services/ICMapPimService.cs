using CMapPim.GeneratedModels;
using CMapPim.Model;
using FluentResults;

namespace CMapPim.Services;

public interface ICMapPimService
{
    public Task<Result<List<RecordSummary>>> GetRecordInformation(
        Context context,
        string startsWith
    );

    Task<Result<ReceivedBase>> GetReceivedTransmittal(Context context, string id);
    Task<Result<ReceivedBase>> SaveReceivedTransmittal(Context context, string id);

    Task<Result<PlaceholderBase>> CreateRecord(Context context, PlaceholderBase record);
    Task<Result<RevisionBase>> CreateRevision(
        Context context,
        PlaceholderBase value,
        RevisionBase newRevision
    );
    Task<Result<RevisionBase>> CreateRendition(
        Context context,
        PlaceholderBase value,
        RevisionBase revision,
        Stream rendition
    );
}
