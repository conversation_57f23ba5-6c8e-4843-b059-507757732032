using System;
using CMapPim.GeneratedModels;
using CMapPim.Model;
using FluentResults;
using Microsoft.Graph.Models;

namespace CMapPim.Services;

public class CMapPimService : ICMapPimService
{
    public async Task<Result<PlaceholderBase>> CreateRecord(Context context, PlaceholderBase record)
    {
        // Create received record
        Result<List<PlaceholderBase>> placeholders = await PlaceholderBase.Find(
            context,
            $"Fields/ATVDocumentNumber eq '{record.ATVDocumentNumber}'"
        );

        if (placeholders.IsFailed)
        {
            return placeholders.ToResult();
        }
        if (placeholders.Value.Count > 0)
        {
            return Result.Ok(placeholders.Value.First()).WithSuccess("Record already exists");
        }
        Result<PlaceholderBase> createdPlaceholder = await record.Create(context);
        if (createdPlaceholder.IsFailed)
        {
            return createdPlaceholder.ToResult();
        }
        // Create default revision

        string revisionDocumentNumber =
            createdPlaceholder.Value.ATVDocumentNumber + "_NEW_DEFAULT_REVISION";
        Result<List<RevisionBase>> defaultRevision = await RevisionBase.Find(
            context,
            $"Fields/ATVDocumentNumber eq '{revisionDocumentNumber}'"
        );

        RevisionBase revision = new(context.Logger);
        revision.ATVRevision = "NEW_DEFAULT_REVISION";
        revision.ATVDocumentNumber = revisionDocumentNumber;
        revision.ATVHideReason = "NEW_DEFAULT_REVISION";
        revision.ATVDateKey = createdPlaceholder.Value.ATVDateKey;
        revision.Title = createdPlaceholder.Value.ATVDocumentNumber;

        Result<RevisionBase> newRevision = await CreateRevision(
            context,
            createdPlaceholder.Value,
            revision
        );

        if (newRevision.IsFailed)
        {
            return newRevision.ToResult();
        }

        return Result.Ok(createdPlaceholder.Value);
    }

    public async Task<Result<RevisionBase>> CreateRendition(
        Context context,
        PlaceholderBase value,
        RevisionBase revision,
        Stream rendition
    )
    {
        string? revisionId = revision.GetId()?.ToString();
        if (string.IsNullOrEmpty(revisionId))
        {
            revisionId = revision.GUID;
        }
        string renditionPath = Flurl.Url.Combine(
            value.ATVDocumentNumber,
            revisionId,
            revision.ATVRevision
        );

        string uploadPath = Flurl.Url.Combine(
            Guid.NewGuid().ToString() + "mail-upload",
            revisionId,
            revision.ATVRevision
        );

        DocumentLibrary atveroUploads = new DocumentLibrary("AtveroUploads");
        Result<string> driveId = await atveroUploads.GetId(context);

        Result<DriveItem?> uploadedFile = await context.GraphApiClient.UploadFileAsync(
            driveId.Value,
            renditionPath,
            rendition,
            context.GraphClient
        );

        if (uploadedFile.IsFailed)
        {
            return uploadedFile.ToResult();
        }
        ListItem listItem = await context.GraphApiClient.GetListItemAsync(
            driveId.Value,
            uploadedFile.Value.Id,
            context.GraphClient
        );
        if (listItem == null)
        {
            return Result.Fail("Failed to get list item");
        }

        Result<AtveroUploadsBase> upload = await AtveroUploadsBase.GetById(context, listItem.Id);
        if (upload.IsFailed)
        {
            return upload.ToResult();
        }

        upload.Value.ATVServerRelativeUrl = value.ATVDocumentNumber;
        upload.Value.ATVFileLength = uploadedFile.Value.Size?.ToString();
        upload.Value.ATVDocumentNumber = value.ATVDocumentNumber;
        upload.Value.ATVDocumentTitle = value.ATVDocumentTitle;
        upload.Value.ATVPlaceholder = value.GetId().Value;
        upload.Value.ATVRevisionLookup = revision.GetId().Value;

        Result<AtveroUploadsBase> updated = await upload.Value.Update(context);

        DocumentLibrary dlmLibrary = new DocumentLibrary("DLM_Library");
        Result<string> destinationDriveId = await dlmLibrary.GetId(context);
        if (destinationDriveId.IsFailed)
        {
            return destinationDriveId.ToResult();
        }

        // Use the sharepoint API to move the file.
        DriveItem destinationPath = await context.GraphApiClient.GetDriveItemByPathAsync(
            destinationDriveId.Value,
            renditionPath,
            context.GraphClient
        );

        Result movedFile = await context.SharepointApi.MoveFile(
            uploadedFile.Value.WebUrl,
            destinationPath.WebUrl,
            true,
            true
        );

        if (movedFile.IsFailed)
        {
            return movedFile;
        }

        Result<DLMFileBase> dlmFile = await DLMFileBase.GetByPath(renditionPath, context);
        // Update revision

        revision.ATVMultiRendition.Add(dlmFile.Value.GetId().Value);
        Result<RevisionBase> updatedRevision = await revision.Update(context);
        if (updatedRevision.IsFailed)
        {
            return updatedRevision.ToResult();
        }

        return Result.Ok(revision);
    }

    public async Task<Result<RevisionBase>> CreateRevision(
        Context context,
        PlaceholderBase record,
        RevisionBase newRevision
    )
    {
        Result<List<RevisionBase>> existingRevisions = await RevisionBase.Find(
            context,
            $"Fields/ATVDocumentNumber eq '{newRevision.ATVDocumentNumber}'"
        );

        if (existingRevisions.IsFailed)
        {
            return existingRevisions.ToResult();
        }
        if (existingRevisions.Value.Count > 0)
        {
            return Result
                .Ok(existingRevisions.Value.First())
                .WithSuccess("Revision already exists");
        }

        if (string.IsNullOrEmpty(newRevision.ATVDocumentNumber))
        {
            newRevision.ATVDocumentNumber = $"{record.ATVDocumentNumber}_{newRevision.ATVRevision}";
        }
        if (string.IsNullOrEmpty(newRevision.Title))
        {
            newRevision.Title = record.ATVDocumentNumber;
        }
        if (string.IsNullOrEmpty(newRevision.ATVDocumentNumber))
        {
            newRevision.Title = record.ATVDocumentNumber;
        }
        if (null == newRevision.ATVDateKey)
        {
            newRevision.ATVDateKey = DateTime.UtcNow;
        }
        int? placeholderId = record.GetId();
        if (null != placeholderId)
        {
            newRevision.ATVPlaceholder = placeholderId.Value;
        }
        if (!string.IsNullOrEmpty(record.GUID))
        {
            newRevision.ATVPlaceholderGuid = record.GUID;
        }
        Result<RevisionBase> rev = await newRevision.Create(context);
        if (rev.IsSuccess)
        {
            int? revisionId = rev.Value.GetId();
            if (null != revisionId)
            {
                record.ATVLastRevision = revisionId.Value;
            }
            Result<PlaceholderBase> updated = await record.Update(context);
            if (updated.IsSuccess)
            {
                return Result.Ok(rev.Value);
            }
            else
            {
                return updated.ToResult();
            }
        }
        else
        {
            return rev.ToResult();
        }
    }

    public Task<Result<ReceivedBase>> GetReceivedTransmittal(Context context, string id)
    {
        throw new NotImplementedException();
    }

    public async Task<Result<List<RecordSummary>>> GetRecordInformation(
        Context context,
        string startsWith
    )
    {
        Result<List<PlaceholderBase>> placeholders;
        if (string.IsNullOrEmpty(startsWith))
        {
            placeholders = await PlaceholderBase.Find(context, "");
        }
        else
        {
            placeholders = await PlaceholderBase.Find(
                context,
                $"startswith(Fields/ATVDocumentNumber,'{startsWith}')"
            );
        }

        if (placeholders.IsFailed)
        {
            return placeholders.ToResult();
        }

        // Find the last revision name for each record
        List<RecordSummary> recordSummaries = new List<RecordSummary>();
        foreach (PlaceholderBase placeholder in placeholders.Value)
        {
            Result<RevisionBase> revisions = await RevisionBase.GetById(
                context,
                placeholder.ATVLastRevision.ToString()
            );
            if (revisions.IsFailed)
            {
                return revisions.ToResult();
            }
            recordSummaries.Add(
                new()
                {
                    Name = placeholder.Title,
                    Title = placeholder.ATVDocumentTitle,
                    LastRevision = revisions.Value.ATVRevision,
                }
            );
        }
        return Result.Ok(recordSummaries);
    }

    public Task<Result<ReceivedBase>> SaveReceivedTransmittal(Context context, string id)
    {
        throw new NotImplementedException();
    }
}
