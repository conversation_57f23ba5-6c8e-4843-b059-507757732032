using System.Net;
using System.Text;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Azure.Storage.Queues;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class SaveAttachmentsAsReceived
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IGraphClientService _graphClientService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SaveAttachmentsAsReceived> _logger;

        public SaveAttachmentsAsReceived(
            ITokenValidationService tokenValidationService,
            IGraphApiClient graphApiClient,
            IGraphClientService graphClientService,
            IConfiguration configuration,
            ILogger<SaveAttachmentsAsReceived> logger
        )
        {
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        [Function("SaveAttachmentsAsReceived")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to save attachments as received at {Time}.",
                DateTime.UtcNow
            );

            try
            {
                // Validate the token
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);
                if (token == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Invalid or missing authorization token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                // Read and parse the request body
                string requestBody;
                using (var streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                }

                SaveAttachmentsAsReceivedRequest? request;
                try
                {
                    request = JsonConvert.DeserializeObject<SaveAttachmentsAsReceivedRequest>(
                        requestBody
                    );
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to decode JSON request");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode request",
                        _logger
                    );
                }

                // Validate request data
                if (
                    request == null
                    || string.IsNullOrEmpty(request.SiteUrl)
                    || string.IsNullOrEmpty(request.EmailId)
                    || request.Attachments == null
                    || !request.Attachments.Any()
                )
                {
                    _logger.LogWarning("Invalid request data: missing siteUrl or attachments.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data. SiteUrl and Attachments are required.",
                        _logger
                    );
                }

                // Validate SharePoint site URL format
                if (!Uri.TryCreate(request.SiteUrl, UriKind.Absolute, out Uri? siteUri))
                {
                    _logger.LogWarning(
                        "Invalid SharePoint site URL format: {SiteUrl}",
                        request.SiteUrl
                    );
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid SharePoint site URL format.",
                        _logger
                    );
                }

                // Validate attachment records
                foreach (var attachment in request.Attachments)
                {
                    if (
                        string.IsNullOrEmpty(attachment.AttachmentId)
                        || string.IsNullOrEmpty(attachment.RecordName)
                        || string.IsNullOrEmpty(attachment.RecordTitle)
                    )
                    {
                        _logger.LogWarning("Invalid attachment record: missing required fields.");
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Invalid attachment record. EmailId, RecordName, and RecordTitle are required.",
                            _logger
                        );
                    }
                }

                // Create GraphServiceClient for the user to validate site access
                var graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );

                if (graphClient == null)
                {
                    _logger.LogError("Failed to create GraphServiceClient");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to create Graph client.",
                        _logger
                    );
                }

                // Get the site information from the SharePoint URL to validate access
                Site? site = await _graphApiClient.GetSiteAsync(request.SiteUrl, graphClient);
                if (site == null)
                {
                    _logger.LogError(
                        "Failed to get site information for URL: {SiteUrl}",
                        request.SiteUrl
                    );
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to access SharePoint site. Please verify the site URL and permissions.",
                        _logger
                    );
                }

                // Extract customer domain from user UPN
                string customerDomain = tokenDetails.Upn?.Split('@').LastOrDefault() ?? "unknown";

                _logger.LogInformation(
                    "Enqueuing {AttachmentCount} attachments for processing for site {SiteUrl} (Site ID: {SiteId})",
                    request.Attachments.Count,
                    request.SiteUrl,
                    site.Id
                );

                // Create queue message
                SaveAttachmentsAsReceivedQueueMessage queueMessage =
                    new SaveAttachmentsAsReceivedQueueMessage
                    {
                        Token = token,
                        UserId = tokenDetails.Upn ?? "unknown_user",
                        CustomerDomain = customerDomain,
                        SiteUrl = request.SiteUrl,
                        SiteId = site.Id,
                        EmailId = request.EmailId,
                        Attachments = request
                            .Attachments.Select(a => new AttachmentRecord
                            {
                                AttachementId = a.AttachementId,
                                RecordName = a.RecordName,
                                RecordTitle = a.RecordTitle,
                                Revision = a.Revision,
                            })
                            .ToList(),
                    };

                // Create QueueClient
                string? connectionString = _configuration["AzureWebJobsStorage"];
                string queueName = "saveattachmentsasreceivedqueue";
                QueueClient queueClient = new QueueClient(connectionString, queueName);

                // Ensure the queue exists
                await queueClient.CreateIfNotExistsAsync();

                // Serialize the queue message and base64 encode it
                var messageString = JsonConvert.SerializeObject(queueMessage);
                var base64Message = Convert.ToBase64String(Encoding.UTF8.GetBytes(messageString));

                try
                {
                    await queueClient.SendMessageAsync(base64Message);
                    _logger.LogInformation(
                        "SaveAttachmentsAsReceived processing queue message enqueued."
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to enqueue message to {QueueName}.", queueName);
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to enqueue processing request.",
                        _logger
                    );
                }

                var response = new
                {
                    SiteUrl = request.SiteUrl,
                    SiteId = site.Id,
                    AttachmentCount = request.Attachments.Count,
                    QueuedAt = DateTime.UtcNow,
                    Message = "Attachments queued for processing",
                    QueueName = queueName,
                };

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    response,
                    "Attachments saved as received successfully"
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
