using System.Text;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using Azure.Storage.Queues.Models;
using CMapPim.Model;
using CMapPim.Services;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class ProcessSaveAttachmentsAsReceived
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IGraphClientService _graphClientService;
        private readonly ICMapPimService _cmapPimService;
        private readonly ILogger<ProcessSaveAttachmentsAsReceived> _logger;

        public ProcessSaveAttachmentsAsReceived(
            ITokenValidationService tokenValidationService,
            IGraphApiClient graphApiClient,
            IGraphClientService graphClientService,
            ICMapPimService cmapPimService,
            ILogger<ProcessSaveAttachmentsAsReceived> logger
        )
        {
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _cmapPimService =
                cmapPimService ?? throw new ArgumentNullException(nameof(cmapPimService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        [Function("ProcessSaveAttachmentsAsReceived")]
        public async Task ProcessMessageJobQueue(
            [QueueTrigger("saveattachmentsasreceivedqueue", Connection = "AzureWebJobsStorage")]
                QueueMessage messageJobQueueMessage
        )
        {
            if (messageJobQueueMessage == null)
            {
                _logger.LogError("Received null QueueMessage in SaveAttachmentsAsReceived Queue.");
                return;
            }

            _logger.LogInformation(
                "Received a dequeue request to process SaveAttachmentsAsReceived at {Time}.",
                DateTime.UtcNow
            );

            SaveAttachmentsAsReceivedQueueMessage? jobMessage = DeserializeMessage(
                messageJobQueueMessage
            );
            if (jobMessage == null)
            {
                _logger.LogWarning("Failed to deserialize job message.");
                return;
            }

            try
            {
                string? token = jobMessage.Token;

                if (token == null)
                {
                    _logger.LogWarning("No token");
                    return;
                }

                DateTime? tokenExpiry = _tokenValidationService.GetTokenExpiryDateTime(token);

                if (tokenExpiry <= DateTime.UtcNow)
                {
                    _logger.LogWarning("Token expired before processing.");
                    return;
                }

                _logger.LogInformation(
                    "Token Expiry is {TokenExpiry} compared to now {Now}",
                    tokenExpiry.ToString(),
                    DateTime.UtcNow.ToString()
                );

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                var graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );

                if (graphClient == null)
                {
                    _logger.LogError("Failed to create GraphServiceClient");
                    return;
                }

                // Create the CMapPim Context
                Context pimContext = new Context
                {
                    SiteId = jobMessage.SiteId,
                    Logger = _logger,
                    GraphApiClient = _graphApiClient,
                    GraphClient = graphClient,
                };

                _logger.LogInformation(
                    "Processing {AttachmentCount} attachments for site {SiteUrl} (Site ID: {SiteId})",
                    jobMessage.Attachments?.Count ?? 0,
                    jobMessage.SiteUrl,
                    jobMessage.SiteId
                );

                if (jobMessage.Attachments != null)
                {
                    foreach (var attachment in jobMessage.Attachments)
                    {
                        try
                        {
                            await ProcessAttachment(pimContext, attachment);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Error processing attachment: EmailId={EmailId}, RecordName={RecordName}",
                                attachment.EmailId,
                                attachment.RecordName
                            );
                        }
                    }
                }

                _logger.LogInformation("Completed processing SaveAttachmentsAsReceived job");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed whilst processing SaveAttachmentsAsReceived message");
            }
        }

        private async Task ProcessAttachment(Context pimContext, AttachmentRecord attachment)
        {
            _logger.LogInformation(
                "Processing attachment: EmailId={EmailId}, RecordName={RecordName}, RecordTitle={RecordTitle}, Revision={Revision}",
                attachment.EmailId,
                attachment.RecordName,
                attachment.RecordTitle,
                attachment.Revision
            );

            // TODO: Implement the actual attachment processing logic here
            // This would typically involve:
            // 1. Using the CMapPim service to find/create records
            // 2. Processing email attachments using the EmailId
            // 3. Saving/updating records in SharePoint lists
            // 4. Handling any file operations

            // Example of how you might use the CMapPim service:
            // var records = await _cmapPimService.GetRecordInformation(pimContext, attachment.RecordName);

            _logger.LogInformation(
                "Successfully processed attachment: EmailId={EmailId}, RecordName={RecordName}",
                attachment.EmailId,
                attachment.RecordName
            );
        }

        private SaveAttachmentsAsReceivedQueueMessage? DeserializeMessage(QueueMessage queueMessage)
        {
            try
            {
                string messageText = queueMessage.MessageText;

                // Try to decode from base64 first
                try
                {
                    byte[] data = Convert.FromBase64String(messageText);
                    messageText = Encoding.UTF8.GetString(data);
                }
                catch
                {
                    // If base64 decoding fails, use the message text as-is
                }

                return JsonConvert.DeserializeObject<SaveAttachmentsAsReceivedQueueMessage>(
                    messageText
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to deserialize SaveAttachmentsAsReceivedQueueMessage");
                return null;
            }
        }
    }
}
