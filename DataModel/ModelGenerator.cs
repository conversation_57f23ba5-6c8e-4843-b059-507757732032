using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using FluentResults;

namespace PimDataModel;

// Model generator class
public class ModelGenerator
{
    // private readonly string schemaPath;
    private readonly string outputPath;
    private readonly bool verbose;

    public ModelGenerator(bool verbose, string outputPath = "../CMapPim/GeneratedModels")
    {
        this.verbose = verbose;
        this.outputPath = outputPath;

        // Ensure output directory exists
        if (!string.IsNullOrEmpty(outputPath) && !Directory.Exists(outputPath))
        {
            Directory.CreateDirectory(outputPath);
        }
    }

    public readonly Dictionary<string, string> classTables = new Dictionary<string, string>()
    {
        { "AtveroUploads", "AtveroUploads" },
        { "AutoNumbers", "AutoNumber" },
        { "Company", "Company" },
        { "CompanyAddress", "CompanyAddress" },
        { "Contact", "Contact" },
        { "ContactsLists", "ContactsList" },
        { "DLM_Placeholders", "Placeholder" },
        { "DLM_Revisions", "Revision" },
        { "Document Groups", "DocumentGroup" },
        { "Document Types", "DocumentType" },
        { "DynamicParts", "DynamicPart" },
        { "HubsiteParts", "HubsitePart" },
        { "Integrations", "Integration" },
        { "Issued_Contacts", "IssuedContact" },
        { "Issued_Revisions", "IssuedRevision" },
        { "Issues", "Issue" },
        { "MetaBlocks", "MetaBlock" },
        { "NameBlocks", "NameBlock" },
        { "NamingSchemes", "NamingScheme" },
        { "ProjectContacts", "ProjectContact" },
        { "ProjectOwners", "ProjectOwner" },
        { "ProjectRoleCategories", "ProjectRoleCategory" },
        { "ProjectRoles", "ProjectRole" },
        { "Projects", "Project" },
        { "Quick Parts", "QuickPart" },
        { "Received_Revisions", "ReceivedRevision" },
        { "Received", "Received" },
        { "RecordLists", "RecordList" },
        { "Schemas", "Schema" },
        { "Sidebars", "Sidebar" },
        { "TenantSettings", "TenantSetting" },
        { "QAReviews", "QAReview" },
        { "DLM_Library", "DLMFile" },
        { "Confidential_DLM_Library", "ConfidentialDLMFile" },
        { "DMS_Library", "DMSFile" },
        { "Confidential_DMS_Library", "ConfidentialDMSFile" },
        { "ISO19650Roles", "ISO19650Role" },
        { "ProjectFactsheet", "ProjectFactsheet" },
        { "ProjectSettings", "ProjectSetting" },
        { "Saved_Filters", "SavedFilter" },
        { "RequiredFields", "RequiredField" },
    };

    List<string> hubListsInOrder = new List<string>
    {
        "Company",
        "CompanyAddress",
        "ProjectRoleCategories",
        "ProjectRoles",
        "Contact",
        "Projects",
        "ContactsLists",
        "Document Groups",
        "Document Types",
        "HubsiteParts",
        "Integrations",
        "ISO19650Roles",
        "MetaBlocks",
        "NamingSchemes",
        "ProjectContacts",
        "ProjectOwners",
        "ProjectSettings",
        "Quick Parts",
        "RecordLists",
        "RequiredFields",
        "Schemas",
        "Sidebars",
        "TenantSettings",
    };

    List<string> siteListsInOrder = new List<string>
    {
        "AtveroUploads",
        "AutoNumbers",
        "DLM_Revisions",
        "DLM_Placeholders",
        "DynamicParts",
        "Integrations",
        "ISO19650Roles",
        "MetaBlocks",
        "NamingSchemes",
        "ProjectFactsheet",
        "ProjectSettings",
        "Quick Parts",
        "Issues",
        "Issued_Contacts",
        "Issued_Revisions",
        "Received",
        "Received_Revisions",
        "RecordLists",
        "Saved_Filters",
    };

    List<string> revisionFields = new List<string>()
    {
        "ATVStatusField",
        "ATVScaleField",
        "ATVDescriptionField",
        "ATVSizeField",
    };

    List<string> placeholderFields = new List<string>()
    {
        "ATVProjectField",
        "ATVCompanyField",
        "ATVNumberField",
        "ATVZoneField",
        "ATVLevelField",
        "ATVRoleField",
        "ATVTypeField",
        "ATVCategoryField",
        "ATVGennumberField",
    };

    public Result GenerateAllModels(string schemaPath)
    {
        Console.WriteLine("Generating models in " + System.IO.Path.GetFullPath(outputPath));
        foreach (string table in classTables.Keys)
        {
            GenerateAtveroModel(schemaPath, classTables[table] + "Base", table, outputPath);
        }

        return Result.Ok();
    }

    bool StringToBool(string val)
    {
        val = val?.ToUpper();

        if (val == null)
        {
            return false;
        }
        if (val == "TRUE" || val == "YES")
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    void GenerateAtveroModel(string schemaPath, string classname, string tablename, string output)
    {
        //Load xml
        XElement xElement = XElement.Load(schemaPath + "/" + tablename + ".xml");

        string listName = xElement.Attribute("Name").Value;
        string listType = xElement.Attribute("Type")?.Value.ToString();

        IEnumerable<XElement> xmlFields = xElement
            .Descendants("Fields")
            .Descendants()
            .Where(item => item.Name == "Field");

        if (xmlFields != null)
        {
            List<string> stringAttributes = new List<string>();
            List<string> intAttributes = new List<string>();
            List<(string, string)> multiLookupAttributes = new List<(string, string)>();
            List<(string, string)> singleLookupAttributes = new List<(string, string)>();
            List<string> boolAttributeNames = new List<string>();
            List<string> dateTimeAttributeNames = new List<string>();

            foreach (XElement fieldXelement in xmlFields)
            {
                bool readOnly = StringToBool(fieldXelement.Attribute("ReadOnly")?.Value);
                string fieldName = fieldXelement.Attribute("Name")?.Value.ToString();

                if (
                    !readOnly
                    && fieldName != "Title"
                    && fieldName != "ATVImportedSourceID"
                    && fieldName != "Created"
                ) // hardcode ATVImportedSourceID later
                {
                    bool isLookupField = fieldXelement.Attribute("Type").Value == "Lookup";
                    bool isMultiLookup = fieldXelement.Attribute("Type").Value == "LookupMulti";

                    string fieldId = fieldXelement.Attribute("ID")?.Value.ToString();

                    string staticName = fieldXelement.Attribute("StaticName")?.Value.ToString();
                    string displayName = fieldXelement.Attribute("DisplayName")?.Value.ToString();
                    string fieldType = fieldXelement.Attribute("Type")?.Value;
                    string description = fieldXelement.Attribute("Description")?.Value;
                    bool indexed = StringToBool(fieldXelement.Attribute("Indexed")?.Value);
                    bool uniqueValues = StringToBool(
                        fieldXelement.Attribute("EnforceUniqueValues")?.Value
                    );
                    bool required = StringToBool(fieldXelement.Attribute("Required")?.Value);

                    if (fieldType == "Text" || fieldType == "Note")
                        stringAttributes.Add(fieldName);

                    if (fieldType == "Number") // probably wrong
                        intAttributes.Add(fieldName);
                    if (fieldType == "LookupMulti")
                    {
                        string list = fieldXelement.Attribute("List")?.Value.ToString();

                        if (classTables.ContainsKey(list))
                        {
                            multiLookupAttributes.Add((fieldName, classTables[list]));
                        }
                        else
                            Console.WriteLine(
                                $"Warning, can't find list {list} defined as lookup {fieldName} in {tablename} for {classname}, skipping"
                            );
                    }
                    if (fieldType == "Lookup")
                    {
                        string list = fieldXelement.Attribute("List")?.Value.ToString();

                        if (classTables.ContainsKey(list))
                        {
                            singleLookupAttributes.Add((fieldName, classTables[list]));
                        }
                        else
                            Console.WriteLine(
                                $"Warning, can't find list {list} defined as lookup {fieldName} in {tablename} for {classname}, skipping"
                            );
                    }
                    if (fieldType == "Boolean")
                        boolAttributeNames.Add(fieldName);
                    if (fieldType == "DateTime")
                        dateTimeAttributeNames.Add(fieldName);
                }
            }

            if (classname == "Placeholder")
            {
                foreach (string field in placeholderFields)
                {
                    stringAttributes.Add(field);
                }
            }

            if (classname == "Revision" || classname == "IssuedRevision")
            {
                foreach (string field in revisionFields)
                {
                    stringAttributes.Add(field);
                }
            }

            // Add Title to every object as it's predefined

            stringAttributes.Add("Title");

            // Contact is special, it has predefined columns

            if (listName == "Contact")
            {
                stringAttributes.Add("FirstName");
                stringAttributes.Add("Email");
                stringAttributes.Add("JobTitle");
                stringAttributes.Add("WorkPhone");
                stringAttributes.Add("HomePhone");
                stringAttributes.Add("CellPhone");
            }

            // Now generate code

            string sourceCode = "";

            sourceCode = sourceCode + "using FluentResults;" + Environment.NewLine;
            sourceCode = sourceCode + "using Microsoft.Extensions.Logging;" + Environment.NewLine;
            sourceCode = sourceCode + "using Sylvan.Data;" + Environment.NewLine;
            sourceCode = sourceCode + "using Sylvan.Data.Csv;" + Environment.NewLine;
            sourceCode =
                sourceCode
                + "using AtveroEmailFiling.Services.GraphApiService;"
                + Environment.NewLine;
            sourceCode = sourceCode + "using CMapPim.Model;" + Environment.NewLine;
            sourceCode = sourceCode + "using Microsoft.Graph.Models;" + Environment.NewLine;

            sourceCode = sourceCode + "namespace CMapPim.GeneratedModels" + Environment.NewLine;
            sourceCode = sourceCode + "{" + Environment.NewLine;
            sourceCode =
                sourceCode + $"  public class {classname} : Model.BaseItem" + Environment.NewLine;
            sourceCode = sourceCode + "   {" + Environment.NewLine;
            sourceCode = sourceCode + "    private string sourceID =  \"0\";" + Environment.NewLine;
            sourceCode =
                sourceCode + "    public static bool exporting = true;" + Environment.NewLine;
            sourceCode =
                sourceCode + "  //  private string _sourcePath =  \"\";" + Environment.NewLine;

            sourceCode =
                sourceCode
                + $"    private static string table = \"{listName}\";"
                + Environment.NewLine;

            sourceCode =
                sourceCode
                + "    private  static string[] stringAttributes = new string[]"
                + Environment.NewLine;
            sourceCode = sourceCode + "    {" + Environment.NewLine;
            sourceCode = sourceCode + @"      ""ATVImportedSourceID""," + Environment.NewLine;
            foreach (string strAttr in stringAttributes)
            {
                sourceCode = sourceCode + $"      \"{strAttr}\"," + Environment.NewLine;
            }
            sourceCode = sourceCode + "    };" + Environment.NewLine;
            sourceCode = sourceCode + "    " + Environment.NewLine;

            sourceCode =
                sourceCode
                + "    private static string[] intAttributes = new string[]"
                + Environment.NewLine;
            sourceCode = sourceCode + "    {" + Environment.NewLine;
            foreach (string strAttr in intAttributes)
            {
                sourceCode = sourceCode + $"      \"{strAttr}\"," + Environment.NewLine;
            }
            sourceCode = sourceCode + "    };" + Environment.NewLine;
            sourceCode = sourceCode + "    " + Environment.NewLine;

            sourceCode =
                sourceCode
                + "    private static string[] multiLookupAttributes = new string[]"
                + Environment.NewLine;
            sourceCode = sourceCode + "    {" + Environment.NewLine;
            foreach ((string strAttr, string lookupListModel) in multiLookupAttributes)
            {
                sourceCode = sourceCode + $"      \"{strAttr}\"," + Environment.NewLine;
            }
            sourceCode = sourceCode + "    };" + Environment.NewLine;
            sourceCode = sourceCode + "    " + Environment.NewLine;

            sourceCode =
                sourceCode
                + "    private static string[] singleLookupAttributes = new string[]"
                + Environment.NewLine;
            sourceCode = sourceCode + "    {" + Environment.NewLine;
            foreach ((string strAttr, string lookupListModel) in singleLookupAttributes)
            {
                sourceCode =
                    sourceCode + $"      \"{strAttr + "LookupId"}\"," + Environment.NewLine;
            }
            sourceCode = sourceCode + "    };" + Environment.NewLine;
            sourceCode = sourceCode + "    " + Environment.NewLine;

            sourceCode =
                sourceCode
                + "    private new static string[] boolAttributeNames = new string[]"
                + Environment.NewLine;
            sourceCode = sourceCode + "    {" + Environment.NewLine;
            foreach (string strAttr in boolAttributeNames)
            {
                sourceCode = sourceCode + $"      \"{strAttr}\"," + Environment.NewLine;
            }
            sourceCode = sourceCode + "    };" + Environment.NewLine;
            sourceCode = sourceCode + "    " + Environment.NewLine;

            sourceCode =
                sourceCode
                + "    private new static string[] dateTimeAttributeNames = new string[]"
                + Environment.NewLine;
            sourceCode = sourceCode + "    {" + Environment.NewLine;
            foreach (string strAttr in dateTimeAttributeNames)
            {
                sourceCode = sourceCode + $"      \"{strAttr}\"," + Environment.NewLine;
            }
            sourceCode = sourceCode + "    };" + Environment.NewLine;
            sourceCode = sourceCode + "  " + Environment.NewLine;

            // we want to export only our data columns
            // so we build that string here too
            // excluding columns we only use on import (e.g. ATVImportedSourceID)

            string export_columns = "";

            // getters and setters



            foreach (string strAttr in stringAttributes)
            {
                sourceCode = sourceCode + "    public string " + strAttr + Environment.NewLine;
                sourceCode = sourceCode + "      {" + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        get {{ return GetFieldAsString(\"{strAttr}\");}}"
                    + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        set {{ SetFieldAsString(\"{strAttr}\",value);}}"
                    + Environment.NewLine;
                sourceCode = sourceCode + "      }" + Environment.NewLine;
                sourceCode = sourceCode + "    " + Environment.NewLine;

                if (strAttr != "ATVImportedSourceID")
                    export_columns = export_columns + $"\"{strAttr}\",";
            }

            foreach (string strAttr in intAttributes)
            {
                sourceCode = sourceCode + "    public int " + strAttr + Environment.NewLine;
                sourceCode = sourceCode + "      {" + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        get {{ return GetFieldAsInteger(\"{strAttr}\");}}"
                    + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        set {{ SetFieldAsInteger(\"{strAttr}\",value);}}"
                    + Environment.NewLine;
                sourceCode = sourceCode + "      }" + Environment.NewLine;
                sourceCode = sourceCode + "    " + Environment.NewLine;

                if (strAttr != "ATVImportedSourceID")
                    export_columns = export_columns + $"\"{strAttr}\",";
            }

            foreach ((string strAttr, string lookupListModel) in multiLookupAttributes)
            {
                // don't use this in import/export as it confuses the CSV reader/writer
                // sourceCode = sourceCode + "    public List<int> " + strAttr + Environment.NewLine;
                // sourceCode = sourceCode + "      {" + Environment.NewLine;
                // sourceCode =
                //   sourceCode
                //   + $@"        get {{

                //      return GetFieldAsMultiLookup(""{strAttr}"");

                //      }}"
                //   + Environment.NewLine;
                // sourceCode =
                //   sourceCode
                //   + $"        set {{ SetFieldAsMultiLookup(\"{strAttr}\",value);}}"
                //   + Environment.NewLine;
                // sourceCode = sourceCode + "      }" + Environment.NewLine;
                // sourceCode = sourceCode + "    " + Environment.NewLine;

                sourceCode =
                    sourceCode
                    + $@"

      public HashSet<int> {strAttr}
      {{
        get {{
          return new HashSet<int>(GetFieldAsMultiLookup(""{strAttr}""));
        }}

        set {{
          SetFieldAsMultiLookup(""{strAttr}"",value.ToList());
        }}

      }}

      ";

                if (strAttr != "ATVImportedSourceID")
                    export_columns = export_columns + $"\"{strAttr}\",";
            }

            foreach ((string strAttr, string lookupListModel) in singleLookupAttributes)
            {
                string modifiedName = strAttr + "LookupId";
                sourceCode = sourceCode + "    public int " + strAttr + Environment.NewLine;
                sourceCode = sourceCode + "      {" + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        get {{ return GetFieldAsSingleLookup(\"{modifiedName}\");}}"
                    + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        set {{ SetFieldAsSingleLookup(\"{modifiedName}\",value);}}"
                    + Environment.NewLine;
                sourceCode = sourceCode + "      }" + Environment.NewLine;
                sourceCode = sourceCode + "    " + Environment.NewLine;

                if (strAttr != "ATVImportedSourceID")
                    export_columns = export_columns + $"\"{strAttr}\",";
            }

            foreach (string strAttr in boolAttributeNames)
            {
                sourceCode = sourceCode + "    public bool " + strAttr + Environment.NewLine;
                sourceCode = sourceCode + "      {" + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        get {{ return GetFieldAsBool(\"{strAttr}\");}}"
                    + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        set {{ SetFieldAsBool(\"{strAttr}\",value);}}"
                    + Environment.NewLine;
                sourceCode = sourceCode + "      }" + Environment.NewLine;
                sourceCode = sourceCode + "    " + Environment.NewLine;

                if (strAttr != "ATVImportedSourceID")
                    export_columns = export_columns + $"\"{strAttr}\",";
            }

            foreach (string strAttr in dateTimeAttributeNames)
            {
                sourceCode = sourceCode + "    public DateTime? " + strAttr + Environment.NewLine;
                sourceCode = sourceCode + "      {" + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        get {{ return GetFieldAsDateTime(\"{strAttr}\");}}"
                    + Environment.NewLine;
                sourceCode =
                    sourceCode
                    + $"        set {{ SetFieldAsDateTime(\"{strAttr}\",value);}}"
                    + Environment.NewLine;
                sourceCode = sourceCode + "      }" + Environment.NewLine;
                sourceCode = sourceCode + "    " + Environment.NewLine;

                if (strAttr != "ATVImportedSourceID")
                    export_columns = export_columns + $"\"{strAttr}\",";
            }

            // remove the trailing ,

            if (export_columns != "")
                export_columns = export_columns.Remove(export_columns.Length - 1, 1);

            sourceCode =
                sourceCode
                + $@"

     // hardcoded for translation


      public string ATVImportedSourceID
      {{
        get {{  if (exporting) return sourceID; else return GetFieldAsString(""ATVImportedSourceID"");}}
        set {{ if (exporting) sourceID = value; else SetFieldAsString(""ATVImportedSourceID"",value);}}
      }}
    ";

            if (listType == "101")
            {
                sourceCode =
                    sourceCode
                    + $@"

      private string? _libraryId;
      private static string? GetLibraryID(Context ctx) {{
        
          CMapPim.Model.DocumentLibrary documentLibrary = new CMapPim.Model.DocumentLibrary(
                    ""{listName}""
                  );

          Result<string> id = documentLibrary.GetId(ctx).GetAwaiter().GetResult();

          if (id.IsFailed)
          {{
            return null;
          }} else {{
            return id.Value;
          }}
     
      }}

      // public string SourcePath {{
      //   get ; set;
      // }}

      public async static Task<Result<{classname}>> GetByPath(string path, Context graphContext)
      {{
        string libraryID = GetLibraryID(graphContext);
        if (libraryID != null) {{
          try
          {{

            ListItem item = await graphContext.GraphApiClient.GetListItemWithPathAsync(
              libraryID,
              path,
              graphContext.GraphClient
            );

            if (item != null)
            {{
              return Result.Ok(new {classname}(new GraphListItem(item), graphContext.Logger));
            }}
            else
            {{
            
              return Result.Fail(""Unable to find item in drive"");
            }}
          }}
          catch (Exception ex)
          {{
       
            return Result.Fail(""Unable to find item in drive"");
          }}
        }} else {{
        
          return Result.Fail(""Failed to find  document library"");
        }}


      }}


/*
      public string GetFilePath(IGraphApiClient graphContext)
      {{


          if (_sourcePath == """") {{

             CMapPim.Model.DocumentLibrary driveLibrary = new CMapPim.Model.DocumentLibrary(
              graphContext,
              ""{listName}""
            );

            Result<Microsoft.Graph.DriveItem> driveItem =
             CMapPim.Model.DriveItemMeta.GetDriveItemByListId(
              graphContext,
              driveLibrary,
              GetId().ToString()
            ).GetAwaiter()
            .GetResult();

            if (driveItem.IsSuccess)
            {{
              string parent = driveItem.Value.ParentReference.Path;
              int pos = parent.IndexOf("":"");
              string cleanedParent = parent.Substring(pos + 1);
              _sourcePath = cleanedParent + ""/"" + driveItem.Value.Name;
            }}

            return _sourcePath;

          }}  else {{
            return _sourcePath;
          }}


      }}




*/
    ";
            }

            sourceCode =
                sourceCode
                + $@"
      private {classname}(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {{
      CopyValues(listItem);
    }}

    ";

            sourceCode =
                sourceCode
                + $@"

    public {classname}(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) {{ }}

    ";

            sourceCode =
                sourceCode
                + $@"

    public {classname}()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) {{ }}

    ";

            sourceCode =
                sourceCode
                + $@"

    public static async Task<Result<{classname}>> GetById(Context ctx, string id)
    {{
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,

                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {{
        return new {classname}(new GraphListItem(limRes.Value), ctx.Logger);
      }}
      else{{
        return limRes.ToResult();
      }}
    }}";

            sourceCode =
                sourceCode
                + $@"

    public static async Task<Result<List<{classname}>>> Find(
      Context ctx,
      string filter
    )
    {{
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {{
        List<{classname}> mappedItems = new List<{classname}>();
        foreach (ListItem item in limRes.Value)
        {{
          mappedItems.Add(new {classname}(new GraphListItem(item), ctx.Logger));
        }}
        return mappedItems;
      }}
      else
        return Result.Fail(limRes.Errors);
    }}";

            sourceCode =
                sourceCode
                + $@"

    public async Task<Result<{classname}>> Create(Context ctx)
    {{
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {{
        return new {classname}(new GraphListItem(limRes.Value), ctx.Logger);
      }}
      else
      {{
        //Console.WriteLine(limRes.Errors.First());
        ctx.Logger.LogError(""There was an error creating the {classname}"");
        return limRes.ToResult();
      }}
    }}";

            sourceCode =
                sourceCode
                + $@"

    public async Task<Result<{classname}>> Update(Context ctx)
    {{
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
     
      if (limRes.IsSuccess && null != limRes.Value)
      {{
        return new {classname}(new GraphListItem(limRes.Value), ctx.Logger);
      }}
      else
      {{
        //Console.WriteLine(limRes.Errors.First());
        ctx.Logger.LogError(""There was an error creating the {classname}"");
        return limRes.ToResult();
      }}
    }}

    ";

            sourceCode =
                sourceCode
                + $@"

    public async Task<Result> Delete(Context ctx)
    {{
          string? id = GetId().ToString();
      if (id == null)
      {{
        return Result.Fail(""No id set"");
      }}
      else
        {{
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }}
    }}

    ";

            sourceCode =
                sourceCode
                + $@"/*
    public static Result Write(List<{classname}> rows, string filePath)
    {{
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select({export_columns});

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }}
*/
     ";

            sourceCode =
                sourceCode
                + $@"/*
    public static List<{classname}> Read(string filePath)
    {{
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {{
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      }};
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<{classname}>().ToList();
    }}
*/
     ";

            //         sourceCode =
            //             sourceCode
            //             + $@"

            //   public static async Task<Result> Export(IGraphApiClient graphContext, string filePath)
            //   {{
            //     Result<List<{classname}>> listRes = await {classname}.Find(graphContext, """", """");

            //     if (listRes.IsSuccess)
            //     {{";

            //         if (listType == "101")
            //         {
            //             sourceCode =
            //                 sourceCode
            //                 + $@"
            //       CMapPim.Model.DocumentLibrary driveLibrary = new CMapPim.Model.DocumentLibrary(
            //       graphContext,
            //       ""{listName}""
            //     );

            //     ";
            //         }

            //         sourceCode =
            //             sourceCode
            //             + $@"
            //         foreach ({classname} c in listRes.Value)
            //         {{
            //           c.ATVImportedSourceID = c.GetId().ToString();
            //           ";

            //         if (listType == "101")
            //         {
            //             sourceCode =
            //                 sourceCode
            //                 + $@"
            //       Result<Microsoft.Graph.DriveItem> driveItem =
            //         await CMapPim.Model.DriveItemMeta.GetDriveItemByListId(
            //           graphContext,
            //           driveLibrary,
            //           c.GetId().ToString()
            //         );

            //       if (driveItem.IsSuccess)
            //       {{
            //         string parent = driveItem.Value.ParentReference.Path;
            //         int pos = parent.IndexOf("":"");
            //         string cleanedParent = parent.Substring(pos + 1);
            //         c.SourcePath = cleanedParent + ""/"" + driveItem.Value.Name;
            //       }}
            //       else
            //       {{
            //         Console.WriteLine(""Failed to find drive item for file "");
            //       }}

            //     ";
            //         }

            //         sourceCode =
            //             sourceCode
            //             + $@"
            //        }}

            //       // write out CSV manifest

            //       {classname}.Write(listRes.Value, filePath);

            //       //logger.LogInformation(""List export complete"");
            //     }}
            //     else
            //     {{
            //       //logger.LogError(""Failed to load list items for export"");
            //       return Result.Fail(""Unable to load list items"");
            //     }}

            //     return Result.Ok();
            //   }}

            // ";

            //         // path finder for imports
            //         if (listType == "101")
            //         {
            //             sourceCode =
            //                 sourceCode
            //                 + $@"
            //     public static Result<{classname}>FindItemById(List<{classname}> items, string id) {{

            //       foreach ({classname} item in items) {{
            //         if (item.ATVImportedSourceID == id) {{
            //           return Result.Ok(item);
            //         }}
            //       }}

            //       return Result.Fail(""Unable to find item in list"");

            //     }}

            //   ";
            //         }

            //         if (listType == "101")
            //         {
            //             sourceCode =
            //                 sourceCode
            //                 + $@"
            //     public static Result<{classname}>FindItemByPath(List<{classname}> items, string path, IGraphApiClient graphContext ) {{

            //       foreach ({classname} item in items) {{
            //         if (item.GetFilePath(graphContext) == path) {{
            //           return Result.Ok(item);
            //         }}
            //       }}

            //       return Result.Fail(""Unable to find item in list"");

            //     }}

            //   ";
            //         }

            //         sourceCode =
            //             sourceCode
            //             + $@"
            //    public async Task FixForImport(IGraphApiClient graphContext,
            //    ILogger logger,
            //    List<DLMFile> dlmFiles,
            //    List<ConfidentialDLMFile> confidentialDlmFiles,
            //    List<DMSFile> dmsFiles,List<ConfidentialDMSFile> confidentialDmsFiles,
            //    List<DLMFile> existingDlmFiles,
            //    List<ConfidentialDLMFile> existingConfidentialDlmFiles,
            //    List<DMSFile> existingDmsFiles,List<ConfidentialDMSFile> existingConfidentialDmsFiles

            //    )
            //    {{
            //       // for each lookup value, search on it by the U2ImportedSourceID and update it

            //     ";

            //         foreach ((string strAttr, string lookupListModel) in singleLookupAttributes)
            //         {
            //             sourceCode =
            //                 sourceCode
            //                 + $@"
            //       if ({strAttr} > 0)
            //       {{

            //         if ( ""{lookupListModel}"" == ""DLMFile"") {{
            //             //
            //             logger.LogDebug(""Fixing a file"");
            //             Result<DLMFile> pathRes = DLMFile.FindItemById(dlmFiles, {strAttr}.ToString());
            //             if (pathRes.IsSuccess) {{
            //               logger.LogDebug(""Found path "" + pathRes.Value.SourcePath);

            //               // Result<DLMFile> importedFile = DLMFile.FindItemByPath(existingDlmFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //    {strAttr} = importedFile.Value.GetId();
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}

            //               Result<string> importedFile = DLMFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr} = (Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}





            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //           else if ( ""{lookupListModel}"" == ""ConfidentialDLMFile"") {{
            //             //
            //             logger.LogDebug(""Fixing a file"");
            //             Result<ConfidentialDLMFile> pathRes = ConfidentialDLMFile.FindItemById(confidentialDlmFiles, {strAttr}.ToString());
            //             if (pathRes.IsSuccess) {{
            //               logger.LogDebug(""Found path "" + pathRes.Value.SourcePath);
            //               // Result<ConfidentialDLMFile> importedFile = ConfidentialDLMFile.FindItemByPath(existingConfidentialDlmFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //    {strAttr} = importedFile.Value.GetId();
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}
            //               Result<string> importedFile = ConfidentialDLMFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr} = (Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}
            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //           else if ( ""{lookupListModel}"" == ""DMSFile"") {{
            //             //
            //             logger.LogDebug(""Fixing a file"");
            //             Result<DMSFile> pathRes = DMSFile.FindItemById(dmsFiles, {strAttr}.ToString());
            //             if (pathRes.IsSuccess) {{
            //               logger.LogDebug(""Found path "" + pathRes.Value.SourcePath);
            //               // Result<DMSFile> importedFile = DMSFile.FindItemByPath(existingDmsFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //    {strAttr} = importedFile.Value.GetId();
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}
            //               Result<string> importedFile = DMSFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr} = (Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}
            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //            else if ( ""{lookupListModel}"" == ""ConfidentialDMSFile"") {{
            //             //
            //            logger.LogDebug(""Fixing a file"");
            //            Result<ConfidentialDMSFile> pathRes = ConfidentialDMSFile.FindItemById(confidentialDmsFiles, {strAttr}.ToString());
            //             if (pathRes.IsSuccess) {{
            //               logger.LogDebug(""Found path "" + pathRes.Value.SourcePath);
            //               // Result<ConfidentialDMSFile> importedFile = ConfidentialDMSFile.FindItemByPath(existingConfidentialDmsFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //    {strAttr} = importedFile.Value.GetId();
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}
            //               Result<string> importedFile = ConfidentialDMSFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr} = (Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}
            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //         else {{
            //           Result<List<{lookupListModel}>> linkedItem = await {lookupListModel}.Find(
            //             graphContext,
            //             ""Fields/ATVImportedSourceID eq "" + {strAttr},
            //             ""Fields""
            //           );
            //           if (linkedItem.IsSuccess)
            //           {{
            //             if (linkedItem.Value.Count > 0)
            //             {{
            //               {strAttr} = linkedItem.Value.First().GetId();
            //             }}
            //             else
            //             {{
            //               logger.LogError(""Failed to find matching ID for {strAttr}"" + {strAttr}.ToString());
            //               {strAttr} = -1;
            //             }}
            //           }}
            //           else
            //           {{
            //             // failed to match
            //             logger.LogError(""Failed to resolve lookup for {strAttr}"");
            //             {strAttr} = -1;
            //           }}
            //         }}
            //       }}

            //       ";
            //         }

            //         foreach ((string strAttr, string lookupListModel) in multiLookupAttributes)
            //         {
            //             // fix multiple lookups
            //             sourceCode =
            //                 sourceCode
            //                 + $@"
            //       //List<int> {strAttr}ids = {strAttr};

            //       List<int> {strAttr}ids = GetFieldAsMultiLookup(""{strAttr}"");

            //       List<int> {strAttr}fixedIds = new List<int>();

            //       if ({strAttr}ids.Count > 0)
            //       {{

            //         foreach (int id in {strAttr}ids) {{

            //           if ( ""{lookupListModel}"" == ""DLMFile"") {{
            //             //
            //             logger.LogDebug(""Fixing a file"");
            //             Result<DLMFile> pathRes = DLMFile.FindItemById(dlmFiles, id.ToString());
            //             if (pathRes.IsSuccess) {{
            //               logger.LogDebug(""Found path "" + pathRes.Value.SourcePath);

            //               // Result<DLMFile> importedFile = DLMFile.FindItemByPath(existingDlmFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //   {strAttr}fixedIds.Add(importedFile.Value.GetId());
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}
            //               Result<string> importedFile = DLMFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr}fixedIds.Add(Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}

            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //           else if ( ""{lookupListModel}"" == ""ConfidentialDLMFile"") {{
            //             //
            //             logger.LogDebug(""Fixing a file"");
            //             Result<ConfidentialDLMFile> pathRes = ConfidentialDLMFile.FindItemById(confidentialDlmFiles, id.ToString());
            //             if (pathRes.IsSuccess) {{
            //               logger.LogDebug(""Found path "" + pathRes.Value.SourcePath);
            //               // Result<ConfidentialDLMFile> importedFile = ConfidentialDLMFile.FindItemByPath(existingConfidentialDlmFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //   {strAttr}fixedIds.Add(importedFile.Value.GetId());
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}
            //               Result<string> importedFile = ConfidentialDLMFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr}fixedIds.Add(Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}
            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //           else if ( ""{lookupListModel}"" == ""DMSFile"") {{
            //             //
            //             logger.LogDebug(""Fixing a file"");
            //             Result<DMSFile> pathRes = DMSFile.FindItemById(dmsFiles, id.ToString());
            //             if (pathRes.IsSuccess) {{
            //               logger.LogDebug(""Found path "" + pathRes.Value.SourcePath);
            //               // Result<DMSFile> importedFile = DMSFile.FindItemByPath(existingDmsFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //   {strAttr}fixedIds.Add(importedFile.Value.GetId());
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}
            //               Result<string> importedFile = DMSFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr}fixedIds.Add(Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}
            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //            else if ( ""{lookupListModel}"" == ""ConfidentialDMSFile"") {{
            //             //
            //            logger.LogDebug(""Fixing a file"");
            //            Result<ConfidentialDMSFile> pathRes = ConfidentialDMSFile.FindItemById(confidentialDmsFiles, id.ToString());
            //             if (pathRes.IsSuccess) {{
            //               // Result<ConfidentialDMSFile> importedFile = ConfidentialDMSFile.FindItemByPath(existingConfidentialDmsFiles, pathRes.Value.SourcePath,graphContext);
            //               // if (importedFile.IsSuccess) {{
            //               //   {strAttr}fixedIds.Add(importedFile.Value.GetId());
            //               // }} else {{
            //               //   logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               // }}
            //               Result<string> importedFile = ConfidentialDMSFile.GetIDByPath(
            //                 pathRes.Value.SourcePath,
            //                 graphContext
            //               );

            //               if (importedFile.IsSuccess)
            //               {{
            //                 {strAttr}fixedIds.Add(Int32.Parse(importedFile.Value));
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Unable to find file for path "" + pathRes.Value.SourcePath);
            //               }}
            //             }} else {{
            //               logger.LogError(""Failed to find item by imported source ID"");
            //             }}
            //           }}
            //           else {{
            //             Result<List<{lookupListModel}>> linkedItem = await {lookupListModel}.Find(
            //               graphContext,
            //               ""Fields/ATVImportedSourceID eq  "" + id.ToString(),
            //               ""Fields""
            //             );
            //             if (linkedItem.IsSuccess)
            //             {{
            //               if (linkedItem.Value.Count > 0)
            //               {{
            //                 {strAttr}fixedIds.Add(linkedItem.Value.First().GetId());
            //               }}
            //               else
            //               {{
            //                 logger.LogError(""Failed to find matching ID for {strAttr}"");

            //               }}
            //             }}
            //             else
            //             {{
            //               // failed to match
            //               logger.LogError(""Failed to resolve lookup for {strAttr}"");

            //             }}
            //           }}
            //         }}
            //         SetFieldAsMultiLookup(""{strAttr}"", {strAttr}fixedIds);

            //       }}

            //     ";
            //         }

            // sourceCode = sourceCode + "    }" + Environment.NewLine;

            sourceCode = sourceCode + "  }" + Environment.NewLine;
            sourceCode = sourceCode + "}" + Environment.NewLine;

            string path = Path.Combine(outputPath, classname + ".cs");
            File.WriteAllText(path, sourceCode);
        }
    }
}
